/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css');

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  color: var(--text-color);
  line-height: 1.6;
  overflow-x: hidden;
  background-color: var(--bg-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Main Container */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-color);
}

main {
  flex: 1;
  padding-top: 80px; /* Account for fixed header */
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
}

/* Section Styles */
section {
  padding: 80px 0;
  transition: background-color 0.3s ease;
}

section:nth-child(odd) {
  background-color: var(--bg-color);
}

section:nth-child(even) {
  background-color: var(--bg-color-alt);
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--text-color);
  position: relative;
}

.section-divider {
  height: 4px;
  width: 70px;
  background-color: var(--primary-color);
  margin: 0 auto 2rem;
  border-radius: 2px;
}

/* Buttons */
.btn {
  padding: 10px 25px;
  border-radius: 5px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Responsive */
@media (max-width: 991.98px) {
  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 767.98px) {
  section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 575.98px) {
  section {
    padding: 40px 0;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
}
