import React, { useState } from 'react';
import { Container, Row, Col, Form, Button, Alert } from 'react-bootstrap';
import { motion } from 'framer-motion';
import '../assets/styles/ContactPage.css';
import siteConfig from '../config/siteConfig';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    moveDate: '',
    message: '',
  });

  const [validated, setValidated] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const form = e.currentTarget;
    
    if (form.checkValidity() === false) {
      e.stopPropagation();
      setValidated(true);
      return;
    }

    // Simulate form submission
    setTimeout(() => {
      setShowSuccess(true);
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        moveDate: '',
        message: '',
      });
      setValidated(false);
      
      // Hide success message after 5 seconds
      setTimeout(() => {
        setShowSuccess(false);
      }, 5000);
    }, 1000);
  };

  return (
    <div className="contact-page">
      {/* Hero Section */}
      <section className="contact-hero">
        <div className="overlay"></div>
        <Container>
          <Row className="align-items-center">
            <Col lg={8} className="mx-auto text-center">
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <h1>Contacteer Ons</h1>
                <p className="hero-subtitle">Heeft u vragen of wilt u een offerte aanvragen? Neem contact met ons op.</p>
              </motion.div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Contact Info Section */}
      <section className="contact-info">
        <Container>
          <Row>
            <Col md={4} className="mb-4">
              <motion.div 
                className="info-card"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <div className="icon-wrapper">
                  <i className="bi bi-geo-alt"></i>
                </div>
                <h3>Adres</h3>
                <p>{siteConfig.address.street}</p>
                <p>{siteConfig.address.postalCode} {siteConfig.address.city}</p>
              </motion.div>
            </Col>
            <Col md={4} className="mb-4">
              <motion.div 
                className="info-card"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <div className="icon-wrapper">
                  <i className="bi bi-telephone"></i>
                </div>
                <h3>Telefoon</h3>
                <p><a href={`tel:${siteConfig.contact.phone}`}>{siteConfig.contact.phoneDisplay}</a></p>
                <p>{siteConfig.businessHours.availability}</p>
              </motion.div>
            </Col>
            <Col md={4} className="mb-4">
              <motion.div 
                className="info-card"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <div className="icon-wrapper">
                  <i className="bi bi-envelope"></i>
                </div>
                <h3>Email</h3>
                <p><a href={`mailto:${siteConfig.contact.email}`}>{siteConfig.contact.emailDisplay}</a></p>
                <p>Snelle reactie binnen 24 uur</p>
              </motion.div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Contact Form Section */}
      <section className="contact-form-section">
        <Container>
          <Row>
            <Col lg={12} className="text-center mb-5">
              <motion.h2
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                Offerte Aanvragen
              </motion.h2>
              <p className="form-subtitle">
                Heeft u vragen over het huren van een verhuislift van {siteConfig.siteName}? Neem contact op. 
                Uw gegevens zijn 100% veilig en worden enkel gebruikt om uw aanvraag te beantwoorden.
              </p>
            </Col>
          </Row>
          <Row className="justify-content-center">
            <Col lg={8}>
              <motion.div
                className="form-container"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                {showSuccess && (
                  <Alert variant="success" onClose={() => setShowSuccess(false)} dismissible>
                    {siteConfig.formMessages.success}
                  </Alert>
                )}
                
                {showError && (
                  <Alert variant="danger" onClose={() => setShowError(false)} dismissible>
                    {siteConfig.formMessages.error}
                  </Alert>
                )}
                
                <Form noValidate validated={validated} onSubmit={handleSubmit}>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3" controlId="formName">
                        <Form.Label>Naam *</Form.Label>
                        <Form.Control
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          required
                          placeholder="Uw volledige naam"
                        />
                        <Form.Control.Feedback type="invalid">
                          Vul uw naam in.
                        </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3" controlId="formEmail">
                        <Form.Label>Email *</Form.Label>
                        <Form.Control
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          placeholder="Uw email adres"
                        />
                        <Form.Control.Feedback type="invalid">
                          Vul een geldig email adres in.
                        </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                  </Row>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3" controlId="formPhone">
                        <Form.Label>Telefoon *</Form.Label>
                        <Form.Control
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          required
                          placeholder="Uw telefoonnummer"
                        />
                        <Form.Control.Feedback type="invalid">
                          Vul uw telefoonnummer in.
                        </Form.Control.Feedback>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3" controlId="formAddress">
                        <Form.Label>Adres</Form.Label>
                        <Form.Control
                          type="text"
                          name="address"
                          value={formData.address}
                          onChange={handleChange}
                          placeholder="Straat, huisnummer, postcode, stad"
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3" controlId="formMoveDate">
                        <Form.Label>Verhuisdatum</Form.Label>
                        <Form.Control
                          type="date"
                          name="moveDate"
                          value={formData.moveDate}
                          onChange={handleChange}
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                  <Form.Group className="mb-3" controlId="formMessage">
                    <Form.Label>Bericht *</Form.Label>
                    <Form.Control
                      as="textarea"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={5}
                      placeholder="Beschrijf uw verhuizing of stel uw vraag..."
                    />
                    <Form.Control.Feedback type="invalid">
                      Vul uw bericht in.
                    </Form.Control.Feedback>
                  </Form.Group>
                  <Form.Group className="mb-3" controlId="formCheck">
                    <Form.Check
                      required
                      label="Ik ga akkoord met de verwerking van mijn gegevens voor het beantwoorden van mijn aanvraag."
                      feedback="U moet akkoord gaan voordat u kunt verzenden."
                      feedbackType="invalid"
                    />
                  </Form.Group>
                  <div className="text-center mt-4">
                    <Button type="submit" className="btn-primary-custom">VERZENDEN</Button>
                  </div>
                </Form>
              </motion.div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Map Section */}
      <section className="map-section">
        <Container fluid className="p-0">
          <Row>
            <Col lg={12} className="p-0">
              <div className="map-container">
                <iframe 
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1248.6017498057306!2d4.443525847923811!3d51.252162404900275!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47c3f7da50621699%3A0xa5d47622e9f1bc44!2sMotaz%20Ladderlift!5e0!3m2!1sar!2sil!4v1747827887889!5m2!1sar!2sil" 
                  width="100%" 
                  height="450" 
                  style={{ border: 0 }} 
                  allowFullScreen="" 
                  loading="lazy" 
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Motaz Ladderlift Location"
                ></iframe>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  );
};

export default ContactPage;
