.ladderlift-content {
  padding: 60px 0;
  background-color: var(--bg-color);
  transition: background-color 0.3s ease;
}

.ladderlift-content h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 30px;
  text-align: center;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.ladderlift-content p {
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 20px;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.btn-primary-custom {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 12px 30px;
  font-weight: 600;
  transition: all 0.3s ease;
  color: white;
}

.btn-primary-custom:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Dark mode specific styles with high contrast */
[data-theme='dark'] .ladderlift-content h2 {
  color: var(--primary-color);
}

[data-theme='dark'] .ladderlift-content p {
  color: #e0e0e0; /* Light gray for good contrast */
}

[data-theme='dark'] .btn-primary-custom {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

[data-theme='dark'] .btn-primary-custom:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-light);
}

@media (max-width: 768px) {
  .ladderlift-content {
    padding: 40px 0;
  }
  
  .ladderlift-content h2 {
    font-size: 1.8rem;
  }
  
  .ladderlift-content p {
    font-size: 1rem;
  }
}
