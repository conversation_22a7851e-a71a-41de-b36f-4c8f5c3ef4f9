.services-section {
  padding: 80px 0;
  background-color: var(--bg-color-alt);
  transition: background-color 0.3s ease;
}

.btn-primary-custom {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 12px 30px;
  font-weight: 600;
  transition: all 0.3s ease;
  color: white;
}

.btn-primary-custom:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 4px 10px var(--shadow-color);
}

/* Dark mode specific styles */
[data-theme='dark'] .services-section {
  background-color: var(--bg-color-alt);
}

[data-theme='dark'] .btn-primary-custom {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

[data-theme='dark'] .btn-primary-custom:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-light);
}

@media (max-width: 768px) {
  .services-section {
    padding: 60px 0;
  }
}
