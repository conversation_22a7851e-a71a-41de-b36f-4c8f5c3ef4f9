/* FAQPage.css */
.faq-page {
  overflow-x: hidden;
}

/* Hero Section */
.faq-hero {
  position: relative;
  background-image: url('/images/ladderlift/WhatsApp Image 2025-05-20 at 3.56.30 PM.jpeg');
  background-size: cover;
  background-position: center;
  padding: 120px 0;
  color: #fff;
  margin-bottom: 60px;
}

.faq-hero .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(58, 123, 213, 0.8), rgba(255, 140, 0, 0.8));
  z-index: 1;
}

.faq-hero .container {
  position: relative;
  z-index: 2;
}

.faq-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 30px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* FAQ Content Section */
.faq-content {
  padding: 60px 0;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.search-container {
  margin-bottom: 40px;
}

.search-container .form-control {
  padding: 15px 20px;
  border-radius: 30px 0 0 30px;
  border: 1px solid #ddd;
  font-size: 1.1rem;
  background-color: var(--bg-color);
  color: var(--text-color);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.search-container .btn {
  border-radius: 0 30px 30px 0;
  padding: 0 20px;
  background-color: var(--alt-bg-color);
  border-color: #ddd;
  color: var(--text-color);
}

.search-container .btn:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.faq-accordion {
  margin-bottom: 40px;
}

.faq-accordion .accordion-item {
  margin-bottom: 15px;
  border: none;
  border-radius: 10px;
  overflow: hidden;
  background-color: var(--alt-bg-color);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.faq-accordion .accordion-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.faq-accordion .accordion-button {
  padding: 20px 25px;
  background-color: var(--alt-bg-color);
  color: var(--text-color);
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: none;
}

.faq-accordion .accordion-button:not(.collapsed) {
  background-color: var(--primary-color);
  color: white;
}

.faq-accordion .accordion-button:focus {
  box-shadow: none;
  border-color: transparent;
}

.faq-accordion .accordion-button::after {
  background-size: 20px;
  transition: transform 0.3s ease;
}

.faq-accordion .accordion-body {
  padding: 20px 25px;
  background-color: var(--alt-bg-color);
  color: var(--text-color);
  font-size: 1.05rem;
  line-height: 1.7;
}

.faq-question {
  display: block;
  padding-right: 20px;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
  background-color: var(--alt-bg-color);
  border-radius: 10px;
}

.no-results i {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
  opacity: 0.7;
}

.no-results h3 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.no-results p {
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.no-results .btn {
  padding: 10px 25px;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.no-results .btn:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Still Have Questions Section */
.still-questions {
  padding: 80px 0;
  background-color: var(--primary-color);
  color: white;
}

.still-questions h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.still-questions p {
  font-size: 1.2rem;
  margin-bottom: 40px;
}

.contact-options {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px;
}

.contact-option {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
  text-align: center;
  padding: 30px 20px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(5px);
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.contact-option:hover {
  transform: translateY(-10px);
  background-color: rgba(255, 255, 255, 0.2);
}

.contact-option i {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.contact-option h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.contact-option p {
  font-size: 0.95rem;
  margin-bottom: 20px;
}

.contact-option .btn {
  padding: 8px 20px;
  border-radius: 30px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.contact-option .btn-primary-custom {
  background-color: white;
  border-color: white;
  color: var(--primary-color);
}

.contact-option .btn-primary-custom:hover {
  background-color: transparent;
  color: white;
  border-color: white;
}

.contact-option .btn-outline-light:hover {
  background-color: white;
  color: var(--primary-color);
}

/* Dark mode adjustments */
[data-theme="dark"] .search-container .form-control {
  background-color: var(--dark-bg-color);
  border-color: #444;
}

[data-theme="dark"] .search-container .btn {
  background-color: var(--dark-bg-color);
  border-color: #444;
}

[data-theme="dark"] .faq-accordion .accordion-item {
  background-color: var(--dark-bg-color);
}

[data-theme="dark"] .faq-accordion .accordion-button {
  background-color: var(--dark-bg-color);
}

[data-theme="dark"] .faq-accordion .accordion-body {
  background-color: var(--dark-bg-color);
}

[data-theme="dark"] .no-results {
  background-color: var(--dark-bg-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .faq-hero h1 {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .still-questions h2 {
    font-size: 1.8rem;
  }
  
  .contact-options {
    flex-direction: column;
    align-items: center;
  }
  
  .contact-option {
    width: 100%;
    max-width: 100%;
  }
}
