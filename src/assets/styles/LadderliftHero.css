.ladderlift-hero {
  position: relative;
  background-image: url('/images/ladderlift/WhatsApp Image 2025-05-20 at 3.56.27 PM.jpeg');
  background-size: cover;
  background-position: center;
  padding: 120px 0;
  color: #fff;
  margin-bottom: 60px;
}

.ladderlift-hero .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(58, 123, 213, 0.8), rgba(255, 140, 0, 0.8));
  z-index: 1;
}

.ladderlift-hero .container {
  position: relative;
  z-index: 2;
}

.ladderlift-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 30px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.ladderlift-features {
  list-style: none;
  padding: 0;
  margin: 0 auto;
  max-width: 500px;
}

.ladderlift-features li {
  font-size: 1.2rem;
  margin-bottom: 15px;
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 5px;
  backdrop-filter: blur(5px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, background-color 0.3s ease;
  color: #ffffff;
}

.ladderlift-features li:hover {
  transform: translateX(5px);
  background-color: rgba(255, 255, 255, 0.25);
}

.ladderlift-features li.emergency-service {
  background-color: rgba(255, 140, 0, 0.4);
  font-weight: 700;
}

/* Dark mode specific styles - ensure text remains highly visible */
[data-theme='dark'] .ladderlift-hero h1,
[data-theme='dark'] .ladderlift-features li {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

[data-theme='dark'] .ladderlift-features li {
  background-color: rgba(255, 255, 255, 0.2);
}

[data-theme='dark'] .ladderlift-features li.emergency-service {
  background-color: rgba(255, 140, 0, 0.5);
}

@media (max-width: 768px) {
  .ladderlift-hero {
    padding: 80px 0;
  }
  
  .ladderlift-hero h1 {
    font-size: 2.2rem;
  }
  
  .ladderlift-features li {
    font-size: 1rem;
    padding: 8px 12px;
  }
}
