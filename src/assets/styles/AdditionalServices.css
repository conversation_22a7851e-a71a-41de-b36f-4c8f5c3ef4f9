.additional-services-section {
  padding: 80px 0;
  background-color: var(--bg-color-alt);
  transition: background-color 0.3s ease;
}

.section-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 15px;
  transition: color 0.3s ease;
}

.section-divider {
  height: 3px;
  background: linear-gradient(to right, transparent, var(--primary-color), transparent);
  width: 80px;
  margin: 0 auto 20px;
}

.section-subtitle {
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
  transition: color 0.3s ease;
}

/* Dark mode specific styles with high contrast */
[data-theme='dark'] .additional-services-section {
  background-color: var(--dark-bg-color-alt);
}

[data-theme='dark'] .section-title {
  color: #ffffff;
}

[data-theme='dark'] .section-divider {
  background: linear-gradient(to right, transparent, var(--primary-light), transparent);
}

[data-theme='dark'] .section-subtitle {
  color: #e0e0e0;
}

@media (max-width: 768px) {
  .additional-services-section {
    padding: 60px 0;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
  
  .section-subtitle {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
  }
}
