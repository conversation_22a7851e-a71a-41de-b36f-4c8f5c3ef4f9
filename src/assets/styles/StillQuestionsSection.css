.still-questions-section {
  padding: 80px 0;
  background: linear-gradient(135deg, rgba(58, 123, 213, 0.9), rgba(255, 140, 0, 0.9));
  color: white;
  position: relative;
  overflow: hidden;
}

.still-questions-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/ladderlift/WhatsApp Image 2025-05-20 at 3.56.30 PM.jpeg');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 0;
}

.still-questions-section .container {
  position: relative;
  z-index: 1;
}

.still-questions-section h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: white;
}

.still-questions-section p {
  font-size: 1.1rem;
  margin-bottom: 40px;
  color: rgba(255, 255, 255, 0.9);
}

.contact-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.contact-option {
  flex: 0 0 calc(33.333% - 30px);
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 30px 20px;
  backdrop-filter: blur(5px);
  transition: transform 0.3s ease, background-color 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-option:hover {
  transform: translateY(-10px);
  background-color: rgba(255, 255, 255, 0.15);
}

.contact-option i {
  font-size: 2.5rem;
  margin-bottom: 15px;
  display: block;
  color: white;
}

.contact-option h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: white;
}

.contact-option p {
  font-size: 0.95rem;
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.9);
}

.btn-outline-light {
  border-color: white;
  color: white;
  padding: 8px 20px;
  transition: all 0.3s ease;
}

.btn-outline-light:hover {
  background-color: white;
  color: var(--primary-color);
}

.btn-primary-custom {
  background-color: white;
  border-color: white;
  color: var(--primary-color);
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary-custom:hover {
  background-color: var(--primary-color);
  border-color: white;
  color: white;
}

/* Dark mode specific styles */
[data-theme='dark'] .still-questions-section {
  background: linear-gradient(135deg, rgba(58, 123, 213, 0.95), rgba(255, 140, 0, 0.95));
}

[data-theme='dark'] .contact-option {
  background-color: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.15);
}

[data-theme='dark'] .contact-option:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

[data-theme='dark'] .btn-primary-custom {
  background-color: white;
  border-color: white;
  color: var(--primary-dark);
}

[data-theme='dark'] .btn-primary-custom:hover {
  background-color: var(--primary-light);
  border-color: white;
  color: white;
}

@media (max-width: 992px) {
  .contact-option {
    flex: 0 0 calc(50% - 30px);
  }
}

@media (max-width: 768px) {
  .still-questions-section {
    padding: 60px 0;
  }
  
  .still-questions-section h2 {
    font-size: 1.8rem;
  }
  
  .still-questions-section p {
    font-size: 1rem;
    margin-bottom: 30px;
  }
  
  .contact-options {
    gap: 20px;
  }
  
  .contact-option {
    flex: 0 0 100%;
    padding: 25px 15px;
  }
  
  .contact-option i {
    font-size: 2rem;
  }
  
  .contact-option h4 {
    font-size: 1.2rem;
  }
}
