.footer {
  background-color: var(--footer-bg);
  color: var(--footer-text);
  padding: 60px 0 30px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.footer h5 {
  color: var(--footer-text);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 10px;
  transition: color 0.3s ease;
}

.footer h5::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  width: 50px;
  background-color: var(--primary-color);
  transition: background-color 0.3s ease;
}

.footer p {
  color: var(--footer-text-muted);
  line-height: 1.8;
  transition: color 0.3s ease;
}

.footer-link {
  color: var(--footer-text-muted);
  transition: all 0.3s ease;
  display: inline-block;
  margin-bottom: 8px;
}

.footer-link:hover {
  color: var(--primary-color);
  transform: translateX(5px);
}

.hours-list li {
  display: flex;
  justify-content: space-between;
  color: var(--footer-text-muted);
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

.contact-list li {
  display: flex;
  margin-bottom: 15px;
}

.contact-list i {
  color: var(--primary-color);
  font-size: 1.2rem;
  margin-top: 4px;
  transition: color 0.3s ease;
}

.contact-list p {
  margin-bottom: 0;
}

.footer hr {
  background-color: rgba(255, 255, 255, 0.1);
}

.footer-logo img {
  max-width: 150px;
  opacity: 1;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 auto;
  max-width: 200px;
}

.logo-circle-bg {
  background-color: var(--card-bg);
  border-radius: 50%;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  box-shadow: 0 4px 15px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.logo-with-name {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.footer-site-name {
  margin-top: -5px;
}

.footer-site-name h4 {
  color: #ff8c00; /* Primary color from the site's color scheme */
  font-weight: 600;
  margin-top: 5px;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  transition: color 0.3s ease;
}

.social-icons {
  display: flex;
  justify-content: flex-end;
}

.social-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 18px;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background-color: var(--primary-color);
  color: #fff;
  transform: translateY(-3px);
}

/* Responsive */
@media (max-width: 767.98px) {
  .footer {
    text-align: center;
  }
  
  .footer h5::after {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .hours-list li,
  .contact-list li {
    justify-content: center;
  }
  
  .contact-list li {
    flex-direction: column;
    align-items: center;
  }
  
  .contact-list i {
    margin-bottom: 5px;
  }
  
  .social-icons {
    justify-content: center;
    margin-bottom: 15px;
  }
  
  .logo-container {
    margin: 0 auto;
  }
  
  .logo-circle-bg {
    width: 180px;
    height: 180px;
  }
  
  .footer-site-name h4 {
    font-size: 0.9rem;
    margin-top: 3px;
  }
}
