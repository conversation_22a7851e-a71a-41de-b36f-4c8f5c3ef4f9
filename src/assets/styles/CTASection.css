/* CTASection.css */
.cta-section {
  padding: 80px 0;
  background-color: var(--bg-color-alt); /* Light gray background in light mode */
  position: relative;
  overflow: hidden;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.cta-section h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--primary-color); /* Orange heading in both modes */
}

.cta-section p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  color: var(--text-color); /* Dark text in light mode, light text in dark mode */
}

.cta-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
}

/* Primary Button Styles */
.btn-primary-custom {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white; /* White text on orange background */
  padding: 12px 30px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary-custom:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Outline Button Styles */
.btn-outline-custom {
  color: var(--primary-color); /* Orange text in light mode */
  border-color: var(--primary-color); /* Orange border in light mode */
  background-color: transparent;
  padding: 12px 30px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-outline-custom:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Dark Mode Adjustments */
[data-theme="dark"] .cta-section {
  background-color: #2a2a2a; /* Darker background in dark mode */
  border-top: 1px solid #444;
  border-bottom: 1px solid #444;
}

[data-theme="dark"] .cta-section p {
  color: #e0e0e0; /* Lighter text for better contrast in dark mode */
}

[data-theme="dark"] .btn-outline-custom {
  color: #e0e0e0; /* Light text in dark mode */
  border-color: #e0e0e0; /* Light border in dark mode */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .cta-section h2 {
    font-size: 1.8rem;
  }
  
  .cta-section p {
    font-size: 1.1rem;
  }
  
  .cta-buttons {
    flex-direction: column;
  }
  
  .btn-primary-custom,
  .btn-outline-custom {
    width: 100%;
    margin-bottom: 10px;
  }
}
