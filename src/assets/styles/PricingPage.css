/* Pricing Hero Section */
.pricing-hero-section {
  background-color: var(--bg-color);
  padding: 100px 0 50px;
  position: relative;
  text-align: center;
  transition: background-color 0.3s ease;
}

.pricing-hero-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  transition: color 0.3s ease;
}

.pricing-hero-subtitle {
  font-size: 1.2rem;
  color: var(--text-color-secondary);
  max-width: 700px;
  margin: 0 auto;
  transition: color 0.3s ease;
}

/* Info Banner */
.pricing-info-banner {
  background-color: var(--primary-bg);
  padding: 20px 0;
  margin-bottom: 50px;
  transition: background-color 0.3s ease;
}

.info-banner-content {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-radius: 8px;
  background-color: var(--card-bg);
  box-shadow: 0 4px 15px var(--shadow-color);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.info-icon {
  font-size: 1.8rem;
  color: var(--primary-color);
  margin-right: 15px;
  flex-shrink: 0;
  transition: color 0.3s ease;
}

.info-banner-content p {
  margin: 0;
  color: var(--text-color);
  font-size: 0.95rem;
  line-height: 1.6;
  transition: color 0.3s ease;
}

/* Pricing Section */
.pricing-section {
  padding: 80px 0;
  background-color: var(--bg-color);
  transition: background-color 0.3s ease;
}

.pricing-section-alt {
  background-color: var(--bg-color-alt);
}

.section-subtitle {
  color: var(--text-color-secondary);
  margin-bottom: 2rem;
  transition: color 0.3s ease;
}

/* Pricing Cards */
.pricing-card {
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg);
  box-shadow: 0 8px 20px var(--shadow-color);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pricing-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px var(--shadow-color);
  background-color: var(--card-bg-hover);
}

.pricing-card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
  transition: color 0.3s ease;
}

.price-badge {
  background-color: var(--primary-color);
  color: white;
  padding: 10px 20px;
  border-radius: 30px;
  display: inline-block;
  margin-bottom: 15px;
  text-align: center;
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  transition: background-color 0.3s ease;
}

.price {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.period {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.9;
}

.pricing-divider {
  height: 3px;
  width: 60px;
  background-color: var(--primary-color);
  margin: 20px auto;
  transition: background-color 0.3s ease;
}

.pricing-features {
  list-style: none;
  padding: 0;
  margin: 0 0 20px;
}

.pricing-features li {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.feature-icon {
  color: var(--primary-color);
  margin-right: 10px;
  font-size: 1.1rem;
  flex-shrink: 0;
  transition: color 0.3s ease;
}

.pricing-notes {
  font-size: 0.9rem;
  color: var(--text-color-muted);
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px dashed var(--border-color);
  transition: color 0.3s ease, border-color 0.3s ease;
}

.pricing-notes p {
  margin-bottom: 8px;
}

.pricing-card-footer {
  margin-top: auto;
  background-color: var(--card-bg-hover);
  border-top: 1px solid var(--border-color);
  padding: 20px;
  text-align: center;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.pricing-btn {
  background-color: var(--primary-color);
  border: none;
  padding: 10px 25px;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;
}

.pricing-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px var(--shadow-color);
}

/* Package Cards */
.package-card .package-header {
  background-color: var(--primary-color);
  padding: 15px;
  text-align: center;
  transition: background-color 0.3s ease;
}

.package-title {
  color: white;
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0;
}

.package-price {
  margin-top: -30px;
  background-color: var(--accent-color);
  z-index: 1;
}

.package-subtitle {
  text-align: center;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  margin-bottom: 0;
  transition: color 0.3s ease;
}

.package-features-title {
  font-size: 1.2rem;
  color: var(--text-color);
  margin-bottom: 20px;
  text-align: center;
  transition: color 0.3s ease;
}

/* Questions Section */
.pricing-questions-section {
  padding: 80px 0;
  background-color: var(--bg-color);
  transition: background-color 0.3s ease;
}

.contact-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  margin-top: 40px;
}

.contact-option {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
  padding: 25px 20px;
  text-align: center;
  background-color: var(--card-bg);
  border-radius: 10px;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: all 0.3s ease;
  text-decoration: none;
  border: 1px solid var(--border-color);
}

.contact-option:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 25px var(--shadow-color);
  background-color: var(--card-bg-hover);
}

.contact-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--primary-bg);
  margin-bottom: 15px;
  transition: background-color 0.3s ease;
}

.contact-icon i {
  font-size: 1.5rem;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.contact-option h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

.contact-option p {
  color: var(--text-color-secondary);
  margin-bottom: 0;
  transition: color 0.3s ease;
}

/* Responsive Styles */
@media (max-width: 991.98px) {
  .pricing-hero-title {
    font-size: 2.5rem;
  }
  
  .contact-options {
    flex-direction: column;
    align-items: center;
  }
  
  .contact-option {
    width: 100%;
    max-width: 100%;
  }
}

@media (max-width: 767.98px) {
  .pricing-hero-title {
    font-size: 2rem;
  }
  
  .pricing-hero-subtitle {
    font-size: 1rem;
  }
  
  .price-badge {
    padding: 8px 15px;
  }
  
  .price {
    font-size: 1.8rem;
  }
  
  .pricing-card-title {
    font-size: 1.3rem;
  }
}

@media (max-width: 575.98px) {
  .pricing-hero-section {
    padding: 80px 0 40px;
  }
  
  .pricing-hero-title {
    font-size: 1.8rem;
  }
  
  .info-banner-content {
    flex-direction: column;
    text-align: center;
  }
  
  .info-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}
