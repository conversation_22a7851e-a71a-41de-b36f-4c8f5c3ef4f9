.faq-hero {
  position: relative;
  background-image: url('/images/ladderlift/WhatsApp Image 2025-05-20 at 3.56.30 PM.jpeg');
  background-size: cover;
  background-position: center;
  padding: 120px 0;
  color: #fff;
  margin-bottom: 60px;
}

.faq-hero .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(58, 123, 213, 0.8), rgba(255, 140, 0, 0.8));
  z-index: 1;
}

.faq-hero .container {
  position: relative;
  z-index: 2;
}

.faq-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  color: #ffffff;
}

.faq-hero .hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 30px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  color: #ffffff;
}

/* Dark mode specific styles - ensure text remains highly visible */
[data-theme='dark'] .faq-hero h1,
[data-theme='dark'] .faq-hero .hero-subtitle {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

@media (max-width: 768px) {
  .faq-hero {
    padding: 80px 0;
  }
  
  .faq-hero h1 {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
}
