.navbar {
  background-color: rgba(255, 255, 255, 0.95);
  padding: 15px 0;
  transition: all 0.3s ease;
}

.navbar.scrolled {
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
}

[data-theme='dark'] .navbar {
  background-color: rgba(26, 26, 26, 0.95);
}

[data-theme='dark'] .navbar.scrolled {
  background-color: rgba(26, 26, 26, 0.98);
}

.navbar-brand {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.logo-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.navbar-brand img {
  max-height: 60px;
  transition: all 0.3s ease;
  object-fit: contain;
}

.navbar.scrolled .navbar-brand img {
  max-height: 50px;
}

.navbar-brand .site-name {
  font-size: 14px;
  font-weight: 600;
  margin-top: 2px;
  transition: all 0.3s ease;
  color: var(--text-color);
}

.navbar.scrolled .navbar-brand .site-name {
  font-size: 12px;
}

[data-theme='dark'] .navbar-brand .site-name {
  color: #fff;
}

.nav-link {
  color: #333333 !important;
  font-weight: 500;
  margin: 0 15px;
  padding: 10px 0;
  position: relative;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
}

[data-theme='dark'] .nav-link {
  color: #ffffff !important;
}

.nav-link:hover {
  color: var(--primary-color) !important;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 3px;
  background-color: var(--primary-color);
  bottom: -5px;
  left: 0;
  transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-link.active {
  color: var(--primary-color) !important;
  font-weight: 600;
}

.phone-number {
  display: flex;
  align-items: center;
}

.phone-link {
  display: flex;
  align-items: center;
  color: var(--primary-color) !important;
  font-weight: 600;
  padding: 8px 15px;
  border-radius: 30px;
  transition: all 0.3s ease;
  background-color: #ffffff;
  text-decoration: none;
}

.phone-link:hover {
  background-color: var(--primary-color);
  color: #ffffff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.navbar-toggler {
  border: none;
  padding: 0;
  position: relative;
  z-index: 1000;
  color: var(--primary-color);
}

.navbar-toggler-fixed {
  position: fixed;
  top: 15px;
  right: 15px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 4px;
  padding: 5px 10px;
  z-index: 1050;
  border: none;
  outline: none;
}

.navbar-toggler:focus {
  box-shadow: none;
}

/* Responsive styles */
@media (max-width: 991.98px) {
  /* Side menu styling */
  .navbar-collapse {
    position: fixed;
    top: 0;
    right: -280px; /* Start off-screen */
    width: 280px;
    height: 100vh;
    background-color: rgba(255, 255, 255, 0.98);
    padding: 80px 20px 20px;
    transition: right 0.3s ease;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    z-index: 1040;
  }
  
  /* When menu is expanded */
  .navbar-collapse.show {
    right: 0;
  }
  
  [data-theme='dark'] .navbar-collapse {
    background-color: rgba(26, 26, 26, 0.98);
  }
  
  .nav-link {
    margin: 10px 0;
    font-size: 18px;
    text-align: right;
    display: block;
    width: 100%;
  }
  
  .contact-info {
    margin-top: 20px;
    text-align: right;
    display: flex;
    justify-content: flex-end;
  }
  
  /* Overlay when menu is open */
  .navbar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1030;
  }
  
  .navbar-overlay.show {
    display: block;
  }
  
  /* Fix container overflow issues */
  .container {
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    overflow-x: hidden;
  }
}

@media (max-width: 575.98px) {
  .navbar {
    padding: 10px 0;
  }
  
  .navbar-brand {
    margin-right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .logo-wrapper {
    width: 70px;
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .navbar-brand img {
    max-height: 65px; /* Significantly increased from original 40px */
    width: auto;
    object-fit: contain;
  }
  
  .navbar.scrolled .navbar-brand img {
    max-height: 55px; /* Increased from original 35px */
  }
  
  .navbar-brand .site-name {
    font-size: 13px;
    margin-top: 3px;
    font-weight: 700;
    color: #ff8c00 !important; /* Using the primary color from the site's color scheme */
  }
  
  .navbar.scrolled .navbar-brand .site-name {
    font-size: 12px;
  }
  
  /* Add some space between the logo and the toggle button */
  .navbar-toggler-fixed {
    margin-left: 10px;
  }
  
  /* Fix mobile layout issues */
  .navbar {
    width: 100%;
    max-width: 100vw;
  }
  
  /* Fix content overflow */
  body {
    overflow-x: hidden;
    max-width: 100%;
  }
}
