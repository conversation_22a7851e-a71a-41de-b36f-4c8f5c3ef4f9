.cost-info-section {
  padding: 80px 0;
  background-color: var(--bg-color);
  transition: background-color 0.3s ease;
}

.cost-container {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--border-color);
}

.cost-container h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.cost-container p {
  font-size: 1.05rem;
  line-height: 1.7;
  margin-bottom: 20px;
  color: var(--text-color-secondary);
  transition: color 0.3s ease;
}

.cost-saving-tips {
  padding-left: 20px;
}

.cost-saving-tips li {
  margin-bottom: 15px;
  font-size: 1.05rem;
  line-height: 1.6;
  color: var(--text-color-secondary);
  transition: color 0.3s ease;
}

.cost-saving-tips li strong {
  color: var(--text-color);
  transition: color 0.3s ease;
}

/* Dark mode specific styles with high contrast */
[data-theme='dark'] .cost-info-section {
  background-color: var(--bg-color);
}

[data-theme='dark'] .cost-container {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

[data-theme='dark'] .cost-container h3 {
  color: #ffffff; /* Pure white for maximum contrast */
}

[data-theme='dark'] .cost-container p,
[data-theme='dark'] .cost-saving-tips li {
  color: #e0e0e0; /* Light gray for good contrast */
}

[data-theme='dark'] .cost-saving-tips li strong {
  color: #ffffff; /* Pure white for maximum contrast */
}

@media (max-width: 768px) {
  .cost-info-section {
    padding: 60px 0;
  }
  
  .cost-container {
    padding: 25px;
  }
  
  .cost-container h3 {
    font-size: 1.3rem;
  }
  
  .cost-container p,
  .cost-saving-tips li {
    font-size: 1rem;
  }
}
