.service-card {
  display: flex;
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 25px;
  height: 100%;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--border-color);
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px var(--shadow-color);
}

.service-icon {
  flex: 0 0 60px;
  height: 60px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  transition: background-color 0.3s ease;
}

.service-icon i {
  font-size: 1.8rem;
}

.service-content {
  flex: 1;
}

.service-content h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.service-content p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 0;
  color: var(--text-color-secondary);
  transition: color 0.3s ease;
}

/* Dark mode specific styles with high contrast */
[data-theme='dark'] .service-card {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

[data-theme='dark'] .service-icon {
  background-color: var(--primary-color);
}

[data-theme='dark'] .service-content h3 {
  color: #ffffff; /* Pure white for maximum contrast */
}

[data-theme='dark'] .service-content p {
  color: #e0e0e0; /* Light gray for good contrast */
}

@media (max-width: 768px) {
  .service-card {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }
  
  .service-icon {
    margin: 0 auto 15px;
  }
  
  .service-content h3 {
    font-size: 1.2rem;
  }
}
