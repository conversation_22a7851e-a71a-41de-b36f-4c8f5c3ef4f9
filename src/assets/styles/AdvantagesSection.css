.advantages-section {
  background-color: var(--bg-color-alt);
  padding: 80px 0;
  transition: background-color 0.3s ease;
}

.advantage-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 30px 20px;
  height: 100%;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  cursor: default;
  border: 1px solid var(--border-color);
}

/* Ensure text is always visible with good contrast */
[data-theme='dark'] .advantage-content p {
  color: var(--text-color) !important;
  font-weight: 500;
}

.advantage-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 25px var(--shadow-color);
  background-color: var(--card-bg-hover);
}

.advantage-icon {
  width: 60px;
  height: 60px;
  background-color: var(--primary-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
  transition: background-color 0.3s ease;
}

.advantage-icon i {
  font-size: 28px;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.advantage-content {
  flex: 1;
}

.advantage-content p {
  margin-bottom: 0;
  font-weight: 500;
  color: var(--text-color);
  line-height: 1.5;
  transition: color 0.3s ease;
}

/* Responsive styles */
@media (max-width: 991.98px) {
  .advantage-card {
    padding: 25px 15px;
  }
  
  .advantage-icon {
    width: 50px;
    height: 50px;
  }
  
  .advantage-icon i {
    font-size: 24px;
  }
}

@media (max-width: 767.98px) {
  .advantage-card {
    margin-bottom: 20px;
  }
}

@media (max-width: 575.98px) {
  .advantage-card {
    flex-direction: column;
    text-align: center;
  }
  
  .advantage-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}
