.work-in-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.work-in-progress-content {
  background-color: white;
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  width: 100%;
}

.work-in-progress-content h1 {
  color: #ff8c00;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.work-in-progress-content h2 {
  color: #333;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.work-in-progress-content p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

@media (max-width: 767.98px) {
  .work-in-progress-content {
    padding: 30px 20px;
  }
  
  .work-in-progress-content h1 {
    font-size: 2rem;
  }
  
  .work-in-progress-content h2 {
    font-size: 1.5rem;
  }
  
  .work-in-progress-content p {
    font-size: 1rem;
  }
}
