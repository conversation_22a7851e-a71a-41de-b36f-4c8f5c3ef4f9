.feature-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 30px 20px;
  height: 100%;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
  text-align: center;
  border: 1px solid var(--border-color);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 25px var(--shadow-color);
}

.icon-wrapper {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: background-color 0.3s ease;
}

.icon-wrapper i {
  font-size: 2rem;
}

.feature-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.feature-card p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: var(--text-color-secondary);
  transition: color 0.3s ease;
}

/* Dark mode specific styles with high contrast */
[data-theme='dark'] .feature-card {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

[data-theme='dark'] .icon-wrapper {
  background-color: var(--primary-color);
}

[data-theme='dark'] .feature-card h3 {
  color: #ffffff; /* Pure white for maximum contrast */
}

[data-theme='dark'] .feature-card p {
  color: #e0e0e0; /* Light gray for good contrast */
}

@media (max-width: 768px) {
  .feature-card {
    padding: 20px 15px;
  }
  
  .icon-wrapper {
    width: 60px;
    height: 60px;
  }
  
  .icon-wrapper i {
    font-size: 1.8rem;
  }
  
  .feature-card h3 {
    font-size: 1.2rem;
  }
}
