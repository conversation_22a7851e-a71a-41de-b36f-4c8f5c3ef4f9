.about-section {
  background-color: var(--bg-color);
  padding: 80px 0;
  transition: background-color 0.3s ease;
}

.about-image {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 10px 30px var(--shadow-color);
  transition: box-shadow 0.3s ease;
}

.about-image img {
  width: 100%;
  transition: transform 0.5s ease;
}

.about-image:hover img {
  transform: scale(1.05);
}

.about-content {
  padding: 20px;
}

.about-text {
  color: var(--text-color-secondary);
  margin-bottom: 20px;
  line-height: 1.8;
  transition: color 0.3s ease;
}

.about-button {
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.about-button.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  transition: all 0.3s ease;
}

.about-button.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

.about-button.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transition: all 0.3s ease;
}

.about-button.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

/* Responsive styles */
@media (max-width: 991.98px) {
  .about-content {
    padding: 20px 0;
    margin-top: 30px;
  }
}

@media (max-width: 767.98px) {
  .about-image {
    margin-bottom: 30px;
  }
  
  .about-content {
    text-align: center;
  }
  
  .section-divider {
    margin: 0 auto 2rem;
  }
}
