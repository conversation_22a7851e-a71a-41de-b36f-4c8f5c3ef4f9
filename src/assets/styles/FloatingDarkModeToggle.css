.floating-dark-mode-toggle {
  position: fixed;
  bottom: 110px; /* Position it above the WhatsApp button */
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--card-bg);
  border: 2px solid var(--primary-color); /* Add border with primary color */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 999;
  box-shadow: 0 3px 15px var(--shadow-color);
  transition: all 0.3s ease;
  color: var(--text-color);
  padding: 0;
  overflow: hidden;
  pointer-events: all;
}

.floating-dark-mode-toggle.light {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.floating-dark-mode-toggle.dark {
  background: linear-gradient(135deg, #2d3436 0%, #000000 100%);
}

.floating-toggle-icon-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.floating-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.floating-dark-mode-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-color);
}

.floating-sun-icon,
.floating-moon-icon {
  font-size: 1.6rem;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
}

.floating-sun-icon {
  color: #ffcc33;
}

.floating-moon-icon {
  color: #c4c9d4;
}

/* Add a glow effect */
.floating-dark-mode-toggle.dark .floating-sun-icon {
  filter: drop-shadow(0 0 5px rgba(255, 204, 51, 0.7));
}

.floating-dark-mode-toggle.light .floating-moon-icon {
  filter: drop-shadow(0 0 3px rgba(61, 90, 254, 0.3));
}

/* For mobile devices */
@media (max-width: 767.98px) {
  .floating-dark-mode-toggle {
    bottom: 90px;
    right: 20px;
    width: 40px;
    height: 40px;
  }
  
  .floating-sun-icon,
  .floating-moon-icon {
    font-size: 1.2rem;
  }
}
