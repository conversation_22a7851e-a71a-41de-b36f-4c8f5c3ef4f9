.testimonial-card {
  height: 100%;
  background-color: var(--card-bg);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid var(--border-color);
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 25px var(--shadow-color);
}

.testimonial-content {
  padding: 30px;
  position: relative;
}

.quote-icon {
  font-size: 2rem;
  color: var(--primary-color);
  opacity: 0.2;
  position: absolute;
  top: 20px;
  left: 20px;
  transition: color 0.3s ease;
}

.testimonial-content p {
  font-size: 1rem;
  line-height: 1.7;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
  padding-top: 20px;
  color: var(--text-color-secondary);
  transition: color 0.3s ease;
}

.testimonial-author {
  display: flex;
  flex-direction: column;
}

.testimonial-author h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.rating {
  color: #ffc107;
  font-size: 0.9rem;
}

/* Dark mode specific styles with high contrast */
[data-theme='dark'] .testimonial-card {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

[data-theme='dark'] .quote-icon {
  color: var(--primary-color);
  opacity: 0.3;
}

[data-theme='dark'] .testimonial-content p {
  color: #e0e0e0; /* Light gray for good contrast */
}

[data-theme='dark'] .testimonial-author h4 {
  color: #ffffff; /* Pure white for maximum contrast */
}

@media (max-width: 768px) {
  .testimonial-content {
    padding: 20px;
  }
  
  .testimonial-content p {
    font-size: 0.95rem;
  }
  
  .testimonial-author h4 {
    font-size: 1rem;
  }
}
