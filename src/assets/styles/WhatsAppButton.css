.whatsapp-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 999;
}

.whatsapp-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: #25D366;
  color: white;
  border-radius: 50%;
  border: 2px solid var(--primary-color); /* Add border with primary color */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.whatsapp-icon i {
  font-size: 30px;
}

.whatsapp-icon:hover {
  background-color: #22c55e;
  transform: scale(1.05);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

/* Responsive styles */
@media (max-width: 767.98px) {
  .whatsapp-button {
    bottom: 20px;
    right: 20px;
  }
  
  .whatsapp-icon {
    width: 50px;
    height: 50px;
  }
  
  .whatsapp-icon i {
    font-size: 25px;
  }
}
