.back-to-top {
  position: fixed;
  bottom: 30px;
  left: 30px;
  z-index: 999;
  width: 45px;
  height: 45px;
  background-color: #ff8c00;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.back-to-top i {
  font-size: 20px;
}

.back-to-top:hover {
  background-color: #e67e00;
  transform: translateY(-5px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive styles */
@media (max-width: 767.98px) {
  .back-to-top {
    bottom: 20px;
    left: 20px;
    width: 40px;
    height: 40px;
  }
  
  .back-to-top i {
    font-size: 18px;
  }
}
