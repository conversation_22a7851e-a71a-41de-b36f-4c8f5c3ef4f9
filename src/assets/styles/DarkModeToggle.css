.dark-mode-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--card-bg);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 9999;
  box-shadow: 0 3px 15px var(--shadow-color);
  transition: all 0.3s ease;
  color: var(--text-color);
  padding: 0;
  overflow: hidden;
  pointer-events: all;
}

.dark-mode-toggle.light {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.dark-mode-toggle.dark {
  background: linear-gradient(135deg, #2d3436 0%, #000000 100%);
}

.toggle-icon-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.dark-mode-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-color);
}

.sun-icon,
.moon-icon {
  font-size: 1.6rem;
  transition: all 0.3s ease;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
}

.sun-icon {
  color: #ffcc33;
}

.moon-icon {
  color: #c4c9d4;
}

/* Add a glow effect */
.dark-mode-toggle.dark .sun-icon {
  filter: drop-shadow(0 0 5px rgba(255, 204, 51, 0.7));
}

.dark-mode-toggle.light .moon-icon {
  filter: drop-shadow(0 0 3px rgba(61, 90, 254, 0.3));
}

/* Add transition for theme change */
body {
  transition: background-color 0.5s ease, color 0.5s ease;
}

* {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* For mobile devices */
@media (max-width: 767.98px) {
  .dark-mode-toggle {
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
  }
  
  .sun-icon,
  .moon-icon {
    font-size: 1.2rem;
  }
}
