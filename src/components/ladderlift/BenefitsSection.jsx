import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { motion } from 'framer-motion';
import BenefitCard from './BenefitCard';
import SectionTitle from '../common/SectionTitle';
import '../../assets/styles/BenefitsSection.css';

const BenefitsSection = () => {
  const benefits = [
    {
      icon: 'clock',
      title: 'Tijdbesparend',
      description: 'Een ladderlift bespaart u veel tijd bij het verhuizen van grote en zware objecten door ramen of balkons.',
      delay: 0.1
    },
    {
      icon: 'shield-check',
      title: '<PERSON>eilig',
      description: 'Voorkom schade aan uw meubels, trappenhuis en muren door gebruik te maken van een professionele ladderlift.',
      delay: 0.2
    },
    {
      icon: 'people',
      title: 'Professionele bediening',
      description: 'Onze ervaren medewerkers zorgen voor een veilige en efficiënte bediening van de ladderlift.',
      delay: 0.3
    }
  ];

  return (
    <section className="benefits-section" id="benefits">
      <Container>
        <Row>
          <Col lg={12} className="mb-5">
            <SectionTitle 
              title="WAAROM EEN LADDERLIFT HUREN?" 
              centered={true}
            />
          </Col>
        </Row>
        <Row>
          {benefits.map((benefit, index) => (
            <Col md={4} className="mb-4" key={index}>
              <BenefitCard 
                icon={benefit.icon}
                title={benefit.title}
                description={benefit.description}
                delay={benefit.delay}
              />
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};

export default BenefitsSection;
