import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { motion } from 'framer-motion';
import '../../assets/styles/LadderliftHero.css';

const LadderliftHero = () => {
  const features = [
    { text: 'Voordelige tarieven', delay: 0.2 },
    { text: 'Geen borg', delay: 0.3 },
    { text: 'Geen verborgen kosten', delay: 0.4 },
    { text: 'Inclusief liften bediener', delay: 0.5 },
    { text: 'Binnen 24 uur beschikbaar', delay: 0.6 },
    { text: '24/7 Spoedservice', delay: 0.7, className: 'emergency-service' }
  ];

  return (
    <section className="ladderlift-hero">
      <div className="overlay"></div>
      <Container>
        <Row className="align-items-center">
          <Col lg={8} className="mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1>VERHUISLIFT HUREN?</h1>
              <ul className="ladderlift-features">
                {features.map((feature, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: feature.delay, duration: 0.5 }}
                    className={feature.className}
                  >
                    {feature.text}
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default LadderliftHero;
