import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { motion } from 'framer-motion';
import PricingCard from './PricingCard';
import { DeliveryTruckIcon } from '../icons/ImageIcons';
import '../../assets/styles/AdditionalServices.css';

const AdditionalServices = () => {
  const additionalServices = [
    {
      price: '€30',
      period: '/ UUR',
      title: 'Extra Sjouwer',
      features: [
        'Voor zware objecten',
        'Professionele hulp',
        'Minimaal 1 uur'
      ],
      notes: [
        'Aanbevolen voor meubels zwaarder dan 100kg.',
        'Betaling ter plaatse met cash of payconiq.'
      ],
      iconComponent: <DeliveryTruckIcon width={80} height={80} />,
      delay: 0.1
    },
    {
      price: '€50',
      period: 'VAST',
      title: 'Parkeervergunning',
      features: [
        'Aanvraag bij gemeente',
        'Gereserveerde parkeerplaats',
        'Inclusief administratie'
      ],
      notes: [
        'Minimaal 5 werkdagen van tevoren aanvragen.',
        'Prijs kan variëren per gemeente.'
      ],
      iconComponent: <DeliveryTruckIcon width={80} height={80} />,
      delay: 0.2
    },
    {
      price: '€40',
      period: 'VAST',
      title: 'Spoedtoeslag',
      features: [
        'Binnen 24 uur service',
        'Prioriteitsplanning',
        'Beschikbaar 7 dagen/week'
      ],
      notes: [
        'Afhankelijk van beschikbaarheid.',
        'Betaling ter plaatse met cash of payconiq.'
      ],
      iconComponent: <DeliveryTruckIcon width={80} height={80} />,
      delay: 0.3
    }
  ];

  return (
    <section className="additional-services-section">
      <Container>
        <Row className="justify-content-center mb-5">
          <Col lg={8} className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="section-title">Aanvullende Diensten</h2>
              <div className="section-divider"></div>
              <p className="section-subtitle">
                Extra services om uw verhuizing nog gemakkelijker te maken
              </p>
            </motion.div>
          </Col>
        </Row>

        <Row className="justify-content-center">
          {additionalServices.map((service, index) => (
            <Col lg={4} md={6} className="mb-4" key={index}>
              <PricingCard 
                price={service.price}
                period={service.period}
                title={service.title}
                features={service.features}
                notes={service.notes}
                iconComponent={service.iconComponent}
                delay={service.delay}
              />
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};

export default AdditionalServices;
