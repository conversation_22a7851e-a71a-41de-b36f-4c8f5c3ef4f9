import React from 'react';
import { Card, Button } from 'react-bootstrap';
import { motion } from 'framer-motion';
import { FaCheck } from 'react-icons/fa';
import '../../assets/styles/PricingCard.css';

const PricingCard = ({ price, period, title, features, notes, iconComponent, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className="h-100"
    >
      <Card className="pricing-card h-100">
        <Card.Body>
          <div className="price-badge">
            <span className="price">{price}</span>
            <span className="period">{period}</span>
          </div>
          <h3 className="pricing-card-title">{title}</h3>
          <div className="pricing-divider"></div>
          <ul className="pricing-features">
            {iconComponent && (
              <div className="d-flex justify-content-center mb-4">
                {iconComponent}
              </div>
            )}
            {features.map((feature, index) => (
              <li key={index}>
                <FaCheck className="feature-icon" />
                <span>{feature}</span>
              </li>
            ))}
          </ul>
          {notes && (
            <div className="pricing-notes">
              {notes.map((note, index) => (
                <p key={index}>{note}</p>
              ))}
            </div>
          )}
        </Card.Body>
        <Card.Footer className="pricing-card-footer">
          <Button className="pricing-btn" href="/contact">RESERVEER</Button>
        </Card.Footer>
      </Card>
    </motion.div>
  );
};

export default PricingCard;
