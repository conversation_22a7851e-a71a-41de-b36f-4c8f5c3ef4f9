import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { motion } from 'framer-motion';
import PricingCard from './PricingCard';
import { CombinedIcons, PersonHelmetIcon, FurnitureTransportIcon } from '../icons/ImageIcons';
import '../../assets/styles/PricingSection.css';

const PricingSection = ({ title, subtitle, alternateBackground = false }) => {
  const pricingOptions = [
    {
      price: '€60',
      period: '/ UUR',
      title: 'Ladderlift met bediener',
      features: [
        '1ste – 5de verdieping',
        'Extra half uur €30',
        'Geldig in Antwerpen (10km)'
      ],
      notes: [
        'Het uur begint bij aankomst en eindigt bij vertrek.',
        'Betaling ter plaatse met cash of payconiq.'
      ],
      iconComponent: <CombinedIcons icons={[PersonHelmetIcon, FurnitureTransportIcon]} width={80} height={80} />,
      delay: 0.1
    },
    {
      price: '€80',
      period: '/ UUR',
      title: 'Ladderlift met bediener',
      features: [
        '6de – 8ste verdieping',
        'Extra half uur €40',
        'G<PERSON>ig in Antwerpen (10km)'
      ],
      notes: [
        'Het uur begint bij aankomst en eindigt bij vertrek.',
        'Betaling ter plaatse met cash of payconiq.'
      ],
      iconComponent: <CombinedIcons icons={[PersonHelmetIcon, FurnitureTransportIcon]} width={80} height={80} />,
      delay: 0.2
    },
    {
      price: '€120',
      period: '/ UUR',
      title: 'Ladderlift met bediener',
      features: [
        '9de -10de verdieping',
        'Extra half uur €60',
        'Geldig in Antwerpen (10km)'
      ],
      notes: [
        'Het uur begint bij aankomst en eindigt bij vertrek.',
        'Betaling ter plaatse met cash of payconiq.'
      ],
      iconComponent: <CombinedIcons icons={[PersonHelmetIcon, FurnitureTransportIcon]} width={80} height={80} />,
      delay: 0.3
    }
  ];

  return (
    <section className={`pricing-section ${alternateBackground ? 'pricing-section-alt' : ''}`}>
      <Container>
        <Row className="justify-content-center mb-5">
          <Col lg={8} className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="section-title">{title}</h2>
              <div className="section-divider"></div>
              <p className="section-subtitle">{subtitle}</p>
            </motion.div>
          </Col>
        </Row>

        <Row className="justify-content-center">
          {pricingOptions.map((option, index) => (
            <Col lg={4} md={6} className="mb-4" key={index}>
              <PricingCard 
                price={option.price}
                period={option.period}
                title={option.title}
                features={option.features}
                notes={option.notes}
                iconComponent={option.iconComponent}
                delay={option.delay}
              />
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};

export default PricingSection;
