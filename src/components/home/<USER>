import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { motion } from 'framer-motion';
import '../../assets/styles/AdvantagesSection.css';

const AdvantagesSection = () => {
  const advantages = [
    {
      id: 1,
      icon: 'bi-people-fill',
      title: 'Verhuislift expert helpt met v<PERSON><PERSON><PERSON><PERSON>, op<PERSON><PERSON>, bedienen én sjouwen'
    },
    {
      id: 2,
      icon: 'bi-grid-3x3-gap-fill',
      title: '4 type verhuisliften: altijd een oplossing op maat'
    },
    {
      id: 3,
      icon: 'bi-box-seam-fill',
      title: 'Extra efficiënt liften door een uitklapbaar verhuisplateau'
    },
    {
      id: 4,
      icon: 'bi-shield-fill-check',
      title: '<PERSON><PERSON><PERSON> gebruik van verhuisdekens en beschermmateriaal'
    },
    {
      id: 5,
      icon: 'bi-chat-square-text-fill',
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> advies van een verhuislift specialist'
    },
    {
      id: 6,
      icon: 'bi-truck',
      title: 'Optioneel: extra sjouwers en een verhuiswagen'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section className="advantages-section py-5">
      <Container>
        <Row className="mb-5">
          <Col lg={8} className="mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
            >
              <h2 className="section-title">Voordelen verhuislift huren bij Motaz Ladderlift</h2>
              <div className="section-divider"></div>
            </motion.div>
          </Col>
        </Row>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          <Row>
            {advantages.map((advantage) => (
              <Col lg={4} md={6} key={advantage.id} className="mb-4">
                <motion.div 
                  className="advantage-card"
                  variants={itemVariants}
                  whileHover={{ y: -10, transition: { duration: 0.3 } }}
                >
                  <div className="advantage-icon">
                    <i className={`bi ${advantage.icon}`}></i>
                  </div>
                  <div className="advantage-content">
                    <p>{advantage.title}</p>
                  </div>
                </motion.div>
              </Col>
            ))}
          </Row>
        </motion.div>
      </Container>
    </section>
  );
};

export default AdvantagesSection;
