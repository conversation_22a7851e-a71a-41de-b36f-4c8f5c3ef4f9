import React from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { motion } from 'framer-motion';
import '../../assets/styles/AboutSection.css';

const AboutSection = () => {
  return (
    <section className="about-section py-5">
      <Container>
        <Row className="align-items-center">
          <Col lg={6} className="mb-4 mb-lg-0">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
              className="about-image"
            >
              <img 
                src="/images/ladderlift/WhatsApp Image 2025-05-20 at 3.48.40 PM.jpeg" 
                alt="Motaz Ladderlift in actie" 
                className="img-fluid rounded shadow"
              />
            </motion.div>
          </Col>
          
          <Col lg={6}>
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
              className="about-content"
            >
              <h2 className="section-title">Ladderlift huren</h2>
              <div className="section-divider mb-4"></div>
              
              <p className="about-text">
                Heeft u een verhuizing in het vooruitzicht en staan de meubels die u gaat 
                verhuizen niet op de begane grond? Sommige meubelstukken passen niet in de 
                lift of zijn te zwaar om via een trap naar beneden te sjouwen. In dat geval 
                biedt een ladderlift uitkomst.
              </p>
              
              <p className="about-text">
                De ladderlift, ook wel verhuislift genoemd, huurt u namelijk eenvoudig bij 
                Motaz Ladderlift. Of u nu op een, twee of drie hoog moet zijn, met een 
                ladderlift van Motaz Ladderlift gereedschapsverhuur verplaatst u op een 
                veilige manier allerlei soorten goederen naar boven en beneden!
              </p>
              
              <div className="mt-4">
                <Button 
                  href="tel:+32469119119" 
                  variant="outline-primary" 
                  className="about-button me-3"
                >
                  <i className="bi bi-telephone-fill me-2"></i>
                  Bel direct
                </Button>
                
                <Button 
                  href="/contact" 
                  variant="primary" 
                  className="about-button"
                >
                  <i className="bi bi-envelope-fill me-2"></i>
                  Offerte aanvragen
                </Button>
              </div>
            </motion.div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default AboutSection;
