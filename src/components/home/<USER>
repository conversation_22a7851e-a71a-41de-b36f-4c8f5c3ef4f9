import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';
import { motion } from 'framer-motion';
import '../../assets/styles/TestimonialsSection.css';

const TestimonialsSection = () => {
  const testimonials = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      rating: 5,
      image: 'https://www.google.com/maps/contrib/109311873145735864723/photos/',
      text: '<PERSON><PERSON> tevreden van de die<PERSON> van <PERSON> ladderlift. Heel vriendelijk en proffesionele personeel, zeer behulpzaam. Ten eerste waren zij stipt op tijd, zij hebben onze spullen behandeld met veel zorg en de verhuis liep heel vlot.',
      date: '2 months ago'
    },
    {
      id: 2,
      name: '<PERSON>',
      rating: 5,
      image: 'https://www.google.com/maps/contrib/104715643230599885242/photos/',
      text: '<PERSON><PERSON> tevreden over deze verhuis service. <PERSON><PERSON><PERSON><PERSON><PERSON> personeel dat flexibel en professioneel werkt. Een aanrader! Zeker een professioneel team. Hebben me goed geholpen.',
      date: '3 months ago'
    },
    {
      id: 3,
      name: '<PERSON> Erreygers',
      rating: 5,
      image: 'https://www.google.com/maps/contrib/111168223790243312511/photos/',
      text: 'Zeer tevreden op tijd, vriendelijk en heel behulpzaam. Zeker een aanrader!',
      date: '3 months ago'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section className="testimonials-section py-5">
      <Container>
        <Row className="mb-5">
          <Col lg={8} className="mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
            >
              <h2 className="section-title">Wat onze klanten zeggen</h2>
              <div className="section-divider"></div>
            </motion.div>
          </Col>
        </Row>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
        >
          <Row>
            {testimonials.map((testimonial) => (
              <Col lg={4} md={6} key={testimonial.id} className="mb-4">
                <motion.div 
                  className="h-100"
                  variants={itemVariants}
                  whileHover={{ y: -10, transition: { duration: 0.3 } }}
                >
                  <Card className="testimonial-card h-100">
                    <Card.Body>
                      <div className="testimonial-rating mb-3">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <i key={i} className="bi bi-star-fill"></i>
                        ))}
                      </div>
                      <Card.Text className="testimonial-text">
                        "{testimonial.text}"
                      </Card.Text>
                    </Card.Body>
                    <Card.Footer className="testimonial-footer">
                      <div className="d-flex align-items-center">
                        <div className="testimonial-avatar">
                          <img 
                            src="/images/avatar-default.svg" 
                            alt={testimonial.name}
                            className="avatar-image"
                          />
                        </div>
                        <div className="ms-3">
                          <h5 className="testimonial-name mb-0">{testimonial.name}</h5>
                          <small className="testimonial-date">{testimonial.date}</small>
                        </div>
                      </div>
                    </Card.Footer>
                  </Card>
                </motion.div>
              </Col>
            ))}
          </Row>
        </motion.div>

        <Row className="mt-4">
          <Col className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <a 
                href="https://www.google.com/search?q=Motaz+ladderlift%2C+Frans+Adriaenssensstraat+25%2C+2170+Antwerpen&sca_esv=18d2e8def591dfb0&hl=ar-IL&gl=il&sxsrf=AHTn8zqtjN16sumQuumZCYV3LGd75Ve9fw%3A1747665524293&ei=dEIraMvYEciP7NYP7LuOqQg&ved=0ahUKEwjL4ou24a-NAxXIB9sEHeydI4UQ4dUDCBA&oq=Motaz+ladderlift%2C+Frans+Adriaenssensstraat+25%2C+2170+Antwerpen&gs_lp=Egxnd3Mtd2l6LXNlcnAiPU1vdGF6IGxhZGRlcmxpZnQsIEZyYW5zIEFkcmlhZW5zc2Vuc3N0cmFhdCAyNSwgMjE3MCBBbnR3ZXJwZW5I3BFQAFgAcAB4AJABAJgB6QKgAekCqgEDMy0xuAEMyAEA-AEC-AEBmAIAoAIAmAMAkgcAoAd3sgcAuAcAwgcAyAcA&sclient=gws-wiz-serp" 
                target="_blank"
                rel="noopener noreferrer"
                className="review-link"
              >
                <i className="bi bi-google me-2"></i>
                Laat een review achter op Google
              </a>
            </motion.div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default TestimonialsSection;
