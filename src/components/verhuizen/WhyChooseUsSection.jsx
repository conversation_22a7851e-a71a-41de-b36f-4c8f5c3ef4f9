import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import SectionTitle from '../common/SectionTitle';
import FeatureCard from './FeatureCard';
import '../../assets/styles/WhyChooseUsSection.css';

const WhyChooseUsSection = () => {
  const features = [
    {
      icon: 'trophy',
      title: 'E<PERSON>ring',
      description: 'Wij hebben al vele verhuizingen succesvol afgerond, en wij weten hoe we uw verhuizing ook tot een succes kunnen maken.',
      delay: 0.1
    },
    {
      icon: 'lightning',
      title: 'Snelle reactie',
      description: 'Na uw aanvraag nemen wij binnen 24 uur contact met u op om uw verhuizing te bespreken.',
      delay: 0.2
    },
    {
      icon: 'emoji-smile',
      title: 'Service met een lach',
      description: '<PERSON><PERSON> is de beste remedie tegen stress, en dat weten wij als geen ander. Daarom zorgen wij ervoor dat er op uw verhuisdag zeker een glimlach vanaf mag.',
      delay: 0.3
    },
    {
      icon: 'file-earmark-text',
      title: '<PERSON><PERSON>lo<PERSON> offerte',
      description: 'Wij nemen uw wensen en behoeften zorgvuldig in overweging. Op basis daarvan maken wij een offerte op maat.',
      delay: 0.4
    }
  ];

  return (
    <section className="why-choose-us-section">
      <Container>
        <Row>
          <Col lg={12} className="mb-5">
            <SectionTitle 
              title="Waarom kiest u voor ons" 
              centered={true}
            />
          </Col>
        </Row>
        <Row>
          {features.map((feature, index) => (
            <Col md={6} lg={3} className="mb-4" key={index}>
              <FeatureCard 
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                delay={feature.delay}
              />
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};

export default WhyChooseUsSection;
