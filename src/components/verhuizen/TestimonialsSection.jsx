import React from 'react';
import { Contain<PERSON>, <PERSON>, Col } from 'react-bootstrap';
import SectionTitle from '../common/SectionTitle';
import TestimonialCard from './TestimonialCard';
import '../../assets/styles/TestimonialsSection.css';

const TestimonialsSection = () => {
  const testimonials = [
    {
      quote: 'Zeer professionele service. De verhuizers waren op tijd, vriendelijk en zorgvuldig met onze spullen. De ladderlift maakte het verhuizen van onze meubels naar de derde verdieping veel gemakkelijker.',
      author: '<PERSON><PERSON>',
      rating: 5,
      delay: 0
    },
    {
      quote: 'Geweldige service! We hadden een last-minute verhuizing en Motaz Ladderlift kon ons binnen 24 uur helpen. De prijs was ook zeer redelijk voor de kwaliteit van de dienstverlening.',
      author: '<PERSON>',
      rating: 5,
      delay: 0.1
    },
    {
      quote: '<PERSON><PERSON> was bang dat mijn antieke piano beschadigd zou raken tij<PERSON><PERSON> de verhuizing, maar het team van Motaz <PERSON>lift heeft hem perfect verplaatst met hun ladderlift. Zeer tevreden!',
      author: '<PERSON>',
      rating: 5,
      delay: 0.2
    }
  ];

  return (
    <section className="testimonials-section">
      <Container>
        <Row>
          <Col lg={12} className="mb-5">
            <SectionTitle 
              title="Wat onze klanten zeggen" 
              centered={true}
            />
          </Col>
        </Row>
        <Row>
          {testimonials.map((testimonial, index) => (
            <Col md={4} className="mb-4" key={index}>
              <TestimonialCard 
                quote={testimonial.quote}
                author={testimonial.author}
                rating={testimonial.rating}
                delay={testimonial.delay}
              />
            </Col>
          ))}
        </Row>
      </Container>
    </section>
  );
};

export default TestimonialsSection;
