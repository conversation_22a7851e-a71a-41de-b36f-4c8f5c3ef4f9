import React from 'react';
import { Container, <PERSON>, Col, Button } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import SectionTitle from '../common/SectionTitle';
import ServiceCard from './ServiceCard';
import '../../assets/styles/ServicesSection.css';

const ServicesSection = () => {
  const services = [
    {
      icon: 'box-seam',
      title: 'In- en uitpakken',
      description: 'Wil je dat het verhuisbedrijf alles voor je inpakt en uitpakt. Of ga je hierin een gedeelte zelf doen.',
      fromLeft: true
    },
    {
      icon: 'tools',
      title: 'Demontage / montage',
      description: 'Heb je grote kasten, bedden of andere meubels dan kun je er voor kiezen om deze te laten demonteren en in het nieuwe huis weer te laten monteren.',
      fromLeft: false
    },
    {
      icon: 'droplet',
      title: 'Schoonmaakservice',
      description: 'Als alles in het nieuwe huis staat wil je eigenlijk gelijk daar aan de slag en heb je geen zin meer om in het oude huis schoon te maken.',
      fromLeft: true,
      delay: 0.2
    },
    {
      icon: 'trash',
      title: 'Afvoeren van goederen',
      description: 'Overbodige spullen, ook dit kunnen wij na de verhuizing voor u weg brengen.',
      fromLeft: false,
      delay: 0.2
    }
  ];

  return (
    <section className="services-section">
      <Container>
        <Row>
          <Col lg={12} className="mb-5">
            <SectionTitle 
              title="De verhuisservice" 
              subtitle="Kies je er voor om alles uit handen te geven of ga je een gedeelte zelf doen. Dit bepaald voor een gedeelte te prijs."
              centered={true}
            />
          </Col>
        </Row>
        <Row>
          {services.map((service, index) => (
            <Col md={6} className="mb-4" key={index}>
              <ServiceCard 
                icon={service.icon}
                title={service.title}
                description={service.description}
                fromLeft={service.fromLeft}
                delay={service.delay || 0}
              />
            </Col>
          ))}
        </Row>
        <Row className="mt-4">
          <Col className="text-center">
            <Link to="/contact">
              <Button className="btn-primary-custom">RESERVEER NU</Button>
            </Link>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default ServicesSection;
