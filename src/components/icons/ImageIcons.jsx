import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

// Helper function to determine if dark mode is active
const isDarkMode = () => {
  return document.documentElement.getAttribute('data-theme') === 'dark';
};

// Icon that changes based on dark/light mode
export const DynamicIcon = ({ 
  darkSrc, 
  lightSrc, 
  alt = "Icon", 
  width = 80, 
  height = 80,
  className = ""
}) => {
  const [isDark, setIsDark] = useState(isDarkMode());
  
  // Listen for theme changes
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          setIsDark(isDarkMode());
        }
      });
    });
    
    observer.observe(document.documentElement, { attributes: true });
    
    return () => observer.disconnect();
  }, []);
  
  return (
    <motion.img
      src={isDark ? darkSrc : lightSrc}
      alt={alt}
      width={width}
      height={height}
      className={className}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    />
  );
};

// Person with Helmet Icon
export const PersonHelmetIcon = ({ width = 80, height = 80, className = "" }) => {
  return (
    <DynamicIcon
      darkSrc="/images/PersonHelmetMoveboxOrang.png"
      lightSrc="/images/PersonHelmetMoveboxB.png"
      alt="Person with Helmet"
      width={width}
      height={height}
      className={className}
    />
  );
};

// Furniture Transport Icon
export const FurnitureTransportIcon = ({ width = 130, height = 130, className = "" }) => {
  const [isDark, setIsDark] = useState(isDarkMode());

  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          setIsDark(isDarkMode());
        }
      });
    });
    
    observer.observe(document.documentElement, { attributes: true });
    
    return () => observer.disconnect();
  }, []);

  return (
    <motion.img
      src="/images/FurnitureTransport.png"
      alt="Furniture Transport"
      style={{ 
        maxWidth: width, 
        height: 'auto', 
        objectFit: 'contain',
        filter: isDark ? 'none' : 'drop-shadow(0 0 1px #000) drop-shadow(0 0 1px #000)',
        transform: 'scale(1.25)',
        margin: '5px'
      }}
      className={className}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    />
  );
};

// Delivery Truck Icon
export const DeliveryTruckIcon = ({ width = 80, height = 80, className = "" }) => {
  return (
    <DynamicIcon
      darkSrc="/images/Delivery_TruckW.png"
      lightSrc="/images/Delivery_TruckB.png"
      alt="Delivery Truck"
      width={width}
      height={height}
      className={className}
    />
  );
};

// Combined icons with plus sign
export const CombinedIcons = ({ icons = [], width = 80, height = 80, className = "" }) => {
  const [isDark, setIsDark] = useState(isDarkMode());
  
  // Listen for theme changes
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          setIsDark(isDarkMode());
        }
      });
    });
    
    observer.observe(document.documentElement, { attributes: true });
    
    return () => observer.disconnect();
  }, []);
  
  return (
    <div className={`d-flex align-items-center justify-content-center ${className}`} style={{ gap: '10px' }}>
      {icons.map((Icon, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.3, delay: 0.2 * index }}
              style={{ fontWeight: 'bold', fontSize: '24px', margin: '0 5px' }}
            >
              <span style={{ color: isDark ? '#fff' : '#333' }}>+</span>
            </motion.div>
          )}
          <motion.div
            initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 * index }}
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center'
            }}
          >
            <Icon 
              width={index === 1 && Icon === FurnitureTransportIcon ? width * 1.3 : width} 
              height={index === 1 && Icon === FurnitureTransportIcon ? height * 1.3 : height} 
            />
          </motion.div>
        </React.Fragment>
      ))}
    </div>
  );
};

export default {
  PersonHelmetIcon,
  FurnitureTransportIcon,
  DeliveryTruckIcon,
  CombinedIcons
};
