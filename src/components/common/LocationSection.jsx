import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { motion } from 'framer-motion';
import SectionTitle from './SectionTitle';
import '../../assets/styles/LocationSection.css';

const LocationSection = () => {
  return (
    <section className="location-section py-5" id="location">
      <Container>
        <Row className="justify-content-center">
          <Col lg={10}>
            <SectionTitle 
              title="Onze Locatie" 
              subtitle="Bezoek ons op onderstaand adres of neem contact op voor meer informatie."
              centered={true}
            />
          </Col>
        </Row>
        
        <Row>
          <Col lg={12}>
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
              className="map-container rounded shadow"
            >
              <iframe 
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1248.6017498057306!2d4.443525847923811!3d51.252162404900275!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47c3f7da50621699%3A0xa5d47622e9f1bc44!2sMotaz%20Ladderlift!5e0!3m2!1sar!2sil!4v1747827887889!5m2!1sar!2sil" 
                width="100%" 
                height="450" 
                style={{ border: 0 }} 
                allowFullScreen="" 
                loading="lazy" 
                referrerPolicy="no-referrer-when-downgrade"
                title="Motaz Ladderlift Location"
                className="google-map"
              ></iframe>
            </motion.div>
          </Col>
        </Row>
        
        <Row className="mt-4">
          <Col md={6} className="mb-3 mb-md-0">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="address-card p-4 h-100"
            >
              <h4><i className="bi bi-geo-alt-fill me-2"></i>Adres</h4>
              <p className="mb-0">Motaz Ladderlift</p>
              <p>Antwerpen, België</p>
            </motion.div>
          </Col>
          
          <Col md={6}>
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="contact-card p-4 h-100"
            >
              <h4><i className="bi bi-telephone-fill me-2"></i>Contact</h4>
              <p className="mb-0">Telefoon: <a href="tel:+32469119119">+32 469 11 91 19</a></p>
              <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </motion.div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default LocationSection;
