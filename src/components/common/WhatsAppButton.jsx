import React from 'react';
import { motion } from 'framer-motion';
import '../../assets/styles/WhatsAppButton.css';
import siteConfig from '../../config/siteConfig';

const WhatsAppButton = () => {
  return (
    <motion.div 
      className="whatsapp-button"
      initial={{ scale: 0, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ 
        type: 'spring',
        stiffness: 260,
        damping: 20,
        delay: 1
      }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
    >
      <a 
        href={siteConfig.contact.whatsapp} 
        target="_blank" 
        rel="noopener noreferrer"
        aria-label="Chat on WhatsApp"
      >
        <div className="whatsapp-icon">
          <i className="bi bi-whatsapp"></i>
        </div>
      </a>
    </motion.div>
  );
};

export default WhatsAppButton;
