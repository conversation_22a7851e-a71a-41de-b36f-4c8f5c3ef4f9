import React, { useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import '../../assets/styles/Footer.css';
import siteConfig from '../../config/siteConfig';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const [isDarkMode, setIsDarkMode] = useState(
    document.documentElement.getAttribute('data-theme') === 'dark'
  );

  useEffect(() => {
    // Function to check if dark mode is active
    const checkTheme = () => {
      const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
      setIsDarkMode(isDark);
    };

    // Initial check
    checkTheme();

    // Create a MutationObserver to watch for changes to the data-theme attribute
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
          checkTheme();
        }
      });
    });

    // Start observing the document element for data-theme attribute changes
    observer.observe(document.documentElement, { attributes: true });

    // Clean up the observer when the component unmounts
    return () => observer.disconnect();
  }, []);

  return (
    <footer className="footer">
      <Container>
        <Row className="py-5">
          <Col lg={4} md={6} className="mb-4 mb-md-0">
            <h5 className="text-uppercase mb-4">{siteConfig.siteName}</h5>
            <p>
              {siteConfig.siteDescription}
              Wij staan klaar om uw verhuizing zo soepel mogelijk te laten verlopen.
            </p>
            <div className="footer-logo mt-3">
              <div className="logo-container">
                <div className="logo-circle-bg">
                  <div className="logo-with-name">
                    <img 
                      src={isDarkMode 
                        ? siteConfig.logos.light
                        : siteConfig.logos.dark} 
                      alt={siteConfig.siteName} 
                      className="img-fluid" 
                      style={{ maxWidth: '120px' }}
                    />
                    <div className="footer-site-name">
                      <h4 className="mt-2 text-center">{siteConfig.siteName}</h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Col>

          <Col lg={2} md={6} className="mb-4 mb-md-0">
            <h5 className="text-uppercase mb-4">Links</h5>
            <ul className="list-unstyled">
              {siteConfig.navigation.map((item, index) => (
                <li className="mb-2" key={index}>
                  <Link to={item.path} className="footer-link">{item.name}</Link>
                </li>
              ))}
            </ul>
          </Col>

          <Col lg={3} md={6} className="mb-4 mb-md-0">
            <h5 className="text-uppercase mb-4">Openingsuren</h5>
            <ul className="list-unstyled hours-list">
              <li className="d-flex justify-content-between mb-2">
                <span>Alle dagen:</span>
                <span>{siteConfig.businessHours.display}</span>
              </li>
            </ul>
          </Col>

          <Col lg={3} md={6} className="mb-4 mb-md-0">
            <h5 className="text-uppercase mb-4">Contact</h5>
            <ul className="list-unstyled contact-list">
              <li className="d-flex mb-3">
                <i className="bi bi-geo-alt-fill me-3"></i>
                <p className="mb-0">
                  <a 
                    href={siteConfig.address.googleMapsUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="footer-link"
                  >
                    {siteConfig.address.street}<br />
                    {siteConfig.address.postalCode} {siteConfig.address.city}
                  </a>
                </p>
              </li>
              <li className="d-flex mb-3">
                <i className="bi bi-telephone-fill me-3"></i>
                <p className="mb-0">
                  <a href={`tel:${siteConfig.contact.phone}`} className="footer-link">
                    {siteConfig.contact.phoneDisplay}
                  </a>
                </p>
              </li>
              <li className="d-flex mb-3">
                <i className="bi bi-whatsapp me-3"></i>
                <p className="mb-0">
                  <a href={siteConfig.contact.whatsapp} target="_blank" rel="noopener noreferrer" className="footer-link">
                    WhatsApp Chat
                  </a>
                </p>
              </li>
              <li className="d-flex mb-3">
                <i className="bi bi-envelope-fill me-3"></i>
                <p className="mb-0">
                  <a href={`mailto:${siteConfig.contact.email}`} className="footer-link">
                    {siteConfig.contact.emailDisplay}
                  </a>
                </p>
              </li>
            </ul>
          </Col>
        </Row>

        <hr className="my-4" />

        <Row className="align-items-center">
          <Col md={6} className="text-center text-md-start">
            <p className="mb-0">
              &copy; {currentYear} {siteConfig.copyright.text}
            </p>
          </Col>
          <Col md={6} className="text-center text-md-end mt-3 mt-md-0">
            <div className="social-icons mb-2">
              <a href={siteConfig.socialMedia.instagram} target="_blank" rel="noopener noreferrer" className="social-icon me-3">
                <i className="bi bi-instagram"></i>
              </a>
              <a href={siteConfig.socialMedia.youtube} target="_blank" rel="noopener noreferrer" className="social-icon me-3">
                <i className="bi bi-youtube"></i>
              </a>
              <a href={siteConfig.socialMedia.tiktok} target="_blank" rel="noopener noreferrer" className="social-icon">
                <i className="bi bi-tiktok"></i>
              </a>
            </div>
            <p className="mb-0">
              Designed by <a href={siteConfig.copyright.designedBy.url} className="footer-link">{siteConfig.copyright.designedBy.name}</a>
            </p>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;
