import React, { useState, useRef } from 'react';
import { Container, Row, Col, Form, Button } from 'react-bootstrap';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { FiUpload, FiFile } from 'react-icons/fi';
import SectionTitle from './SectionTitle';
import '../../assets/styles/ContactSectionComponent.css';

const ContactSectionComponent = () => {
  const { register, handleSubmit, formState: { errors }, reset } = useForm();
  const [fileName, setFileName] = useState('');
  const fileInputRef = useRef(null);

  const onSubmit = (data) => {
    console.log(data);
    // Here you would typically send the form data to a server
    // For now, we'll just reset the form
    reset();
    setFileName('');
    alert('Bedankt voor uw bericht! We nemen zo spoedig mogelijk contact met u op.');
  };
  
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setFileName(e.target.files[0].name);
    } else {
      setFileName('');
    }
  };
  
  const handleFileButtonClick = () => {
    fileInputRef.current.click();
  };

  return (
    <section className="contact-section py-5" id="contact">
      <Container>
        <Row className="mb-5">
          <Col lg={8} className="mx-auto">
            <SectionTitle 
              title="Contacteer Ons" 
              subtitle="Heeft u vragen over het huren van een verhuislift van Motaz Ladderlift? Neem contact op. Uw gegevens zijn 100% veilig en worden enkel gebruikt om uw aanvraag te beantwoorden."
              centered={true}
            />
          </Col>
        </Row>

        <Row className="justify-content-center">
          <Col lg={5} md={6} className="mb-4 mb-md-0">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
              className="contact-info-card"
            >
              <h3 className="contact-info-title">Main Office</h3>
              
              <div className="contact-info-item">
                <i className="bi bi-geo-alt-fill"></i>
                <div>
                  <a 
                    href="https://maps.app.goo.gl/Yx6Yx4yvdtDGYXwW7" 
                    target="_blank" 
                    rel="noopener noreferrer"
                  >
                    Frans Adriaenssensstraat 25<br />
                    2170 Antwerpen
                  </a>
                </div>
              </div>
              
              <div className="contact-info-item">
                <i className="bi bi-telephone-fill"></i>
                <div>
                  <a href="tel:+32469119119">
                  +32 469 11 91 19
                  </a>
                </div>
              </div>
              
              <div className="contact-info-item">
                <i className="bi bi-envelope-fill"></i>
                <div>
                  <a href="mailto:<EMAIL>">
                    <EMAIL>
                  </a>
                </div>
              </div>
              
              <div className="mt-4">
                <h4 className="contact-info-subtitle">Openingsuren</h4>
                <ul className="contact-hours-list">
                  <li>
                    <span>Alle dagen:</span>
                    <span>24 uur / 7 dagen</span>
                  </li>
                </ul>
              </div>
              
              <div className="mt-4">
                <iframe 
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1248.6017498057306!2d4.443525847923811!3d51.252162404900275!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47c3f7da50621699%3A0xa5d47622e9f1bc44!2sMotaz%20Ladderlift!5e0!3m2!1sar!2sil!4v1747827887889!5m2!1sar!2sil" 
                  width="100%" 
                  height="200" 
                  style={{ border: 0, borderRadius: '8px' }}
                  allowFullScreen="" 
                  loading="lazy" 
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Motaz Ladderlift Location"
                ></iframe>
              </div>
            </motion.div>
          </Col>
          
          <Col lg={7} md={6}>
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
              className="contact-form-card"
            >
              <h3 className="contact-form-title">Offerte Aanvragen</h3>
              
              <Form onSubmit={handleSubmit(onSubmit)}>
                <Row>
                  <Col md={6} className="mb-3">
                    <Form.Group>
                      <Form.Label>Naam *</Form.Label>
                      <Form.Control 
                        type="text" 
                        placeholder="Uw naam" 
                        {...register('name', { required: true })}
                        isInvalid={!!errors.name}
                      />
                      {errors.name && (
                        <Form.Control.Feedback type="invalid">
                          Naam is verplicht
                        </Form.Control.Feedback>
                      )}
                    </Form.Group>
                  </Col>
                  
                  <Col md={6} className="mb-3">
                    <Form.Group>
                      <Form.Label>E-mail *</Form.Label>
                      <Form.Control 
                        type="email" 
                        placeholder="Uw e-mail" 
                        {...register('email', { 
                          required: true,
                          pattern: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i
                        })}
                        isInvalid={!!errors.email}
                      />
                      {errors.email && (
                        <Form.Control.Feedback type="invalid">
                          Geldig e-mailadres is verplicht
                        </Form.Control.Feedback>
                      )}
                    </Form.Group>
                  </Col>
                </Row>
                
                <Row>
                  <Col md={6} className="mb-3">
                    <Form.Group>
                      <Form.Label>Telefoon *</Form.Label>
                      <Form.Control 
                        type="tel" 
                        placeholder="Uw telefoonnummer" 
                        {...register('phone', { required: true })}
                        isInvalid={!!errors.phone}
                      />
                      {errors.phone && (
                        <Form.Control.Feedback type="invalid">
                          Telefoonnummer is verplicht
                        </Form.Control.Feedback>
                      )}
                    </Form.Group>
                  </Col>
                  
                  <Col md={6} className="mb-3">
                    <Form.Group>
                      <Form.Label>Datum van verhuizing</Form.Label>
                      <Form.Control 
                        type="date" 
                        {...register('moveDate')}
                      />
                    </Form.Group>
                  </Col>
                </Row>
                
                <Form.Group className="mb-3">
                  <Form.Label>Bericht *</Form.Label>
                  <Form.Control 
                    as="textarea" 
                    rows={4} 
                    placeholder="Beschrijf uw verhuizing en specifieke behoeften..." 
                    {...register('message', { required: true })}
                    isInvalid={!!errors.message}
                  />
                  {errors.message && (
                    <Form.Control.Feedback type="invalid">
                      Bericht is verplicht
                    </Form.Control.Feedback>
                  )}
                </Form.Group>
                
                <Form.Group className="mb-4">
                  <Form.Label>Upload foto's van uw locatie (optioneel)</Form.Label>
                  <div className="file-upload-wrapper">
                    <input
                      type="file"
                      className="d-none"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      accept="image/*"
                      {...register('photos')}
                    />
                    <div className="custom-file-upload" onClick={handleFileButtonClick}>
                      <FiUpload className="me-2" />
                      Kies bestand
                    </div>
                    {fileName && (
                      <div className="selected-file">
                        <FiFile className="me-2" />
                        {fileName}
                      </div>
                    )}
                  </div>
                  <Form.Text className="text-muted">
                    Foto's helpen ons om een nauwkeurigere offerte te maken.
                  </Form.Text>
                </Form.Group>
                
                <Button type="submit" className="contact-submit-btn">
                  Verstuur Aanvraag
                </Button>
              </Form>
            </motion.div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default ContactSectionComponent;
