import React, { useState, useEffect } from 'react';
import { Navbar, Container, Nav } from 'react-bootstrap';
import { motion } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import '../../assets/styles/Header.css';
import siteConfig from '../../config/siteConfig';

const Header = () => {
  const [scrolled, setScrolled] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(
    document.documentElement.getAttribute('data-theme') === 'dark'
  );
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    // Function to check if dark mode is active
    const checkTheme = () => {
      const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
      setIsDarkMode(isDark);
    };

    // Initial check
    checkTheme();

    // Create a MutationObserver to watch for changes to the data-theme attribute
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
          checkTheme();
        }
      });
    });

    // Start observing the document element for data-theme attribute changes
    observer.observe(document.documentElement, { attributes: true });

    // Clean up the observer when the component unmounts
    return () => observer.disconnect();
  }, []);

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Overlay for when mobile menu is open */}
      <div 
        className={`navbar-overlay ${expanded ? 'show' : ''}`} 
        onClick={() => setExpanded(false)}
      ></div>
      
      <Navbar 
        expand="lg" 
        className={`navbar ${scrolled ? 'scrolled' : ''}`} 
        fixed="top"
        expanded={expanded}
        onToggle={() => setExpanded(!expanded)}
        variant={isDarkMode ? "dark" : "light"}
      >
        <Container>
          <Navbar.Brand as={Link} to="/" className="d-flex flex-column align-items-center brand-container">
            <div className="logo-wrapper">
              <img 
                src={isDarkMode ? siteConfig.logos.light : siteConfig.logos.dark} 
                alt={siteConfig.siteName} 
                className="logo"
                height="60"
              />
            </div>
            <span className="site-name" style={{
              fontSize: '14px',
              fontWeight: '600',
              marginTop: '2px',
              color: isDarkMode ? '#fff' : '#333',
              letterSpacing: '0.5px',
              transition: 'all 0.3s ease'
            }}>
              {siteConfig.siteName}
            </span>
          </Navbar.Brand>
          
          <Navbar.Toggle aria-controls="basic-navbar-nav" className="navbar-toggler-fixed" />
          
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="mx-auto">
              {siteConfig.navigation.map((item, index) => (
                <Nav.Link 
                  key={index}
                  as={Link} 
                  to={item.path} 
                  className={`nav-link ${location.pathname === item.path ? 'active' : ''}`} 
                  onClick={() => setExpanded(false)}
                >
                  {item.name}
                </Nav.Link>
              ))}
            </Nav>
            
            <div className="phone-number">
              <a href={`tel:${siteConfig.contact.phone}`} className="phone-link">
                <i className="bi bi-telephone-fill me-2"></i>
                {siteConfig.contact.phoneDisplay}
              </a>
            </div>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </motion.header>
  );
};

export default Header;
