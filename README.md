# Motaz Ladderlift Website Redesign

## Overview
This project is a modern redesign of the [Mo<PERSON>z Ladderlift website](https://Motazladderlift.be/) using React, Vite, and Bootstrap 5. The redesign focuses on creating a responsive, user-friendly interface with modern animations and improved user experience.

## Features
- Modern, responsive design using Bootstrap 5
- Interactive UI with animations using Framer Motion and AOS
- Mobile-friendly navigation
- Contact form with validation
- Floating WhatsApp button for quick contact
- Back-to-top button for improved navigation
- Testimonials section showcasing customer reviews

## Technology Stack
- **Frontend Framework**: React 18
- **Build Tool**: Vite
- **CSS Framework**: Bootstrap 5
- **Animation Libraries**: Framer Motion, AOS (Animate On Scroll)
- **Form Handling**: React Hook Form
- **Routing**: React Router

## Project Structure
```
Motazladderlift/
├── public/
│   ├── images/            # Website images
│   └── favicon.ico        # Website favicon
├── src/
│   ├── assets/
│   │   ├── images/        # Image assets
│   │   └── styles/        # CSS stylesheets
│   ├── components/
│   │   ├── common/        # Common components (Header, Footer, etc.)
│   │   ├── home/          # Homepage components
│   │   └── ui/            # UI components
│   ├── pages/             # Page components
│   ├── App.jsx            # Main App component
│   └── main.jsx           # Entry point
└── index.html             # HTML template
```

## Getting Started

### Prerequisites
- Node.js (v14.0.0 or later)
- npm (v6.0.0 or later)

### Installation
1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Start the development server:
   ```
   npm run dev
   ```
4. Open your browser and navigate to `http://localhost:5173`

## Building for Production
To build the project for production, run:
```
npm run build
```

The build artifacts will be stored in the `dist/` directory.
